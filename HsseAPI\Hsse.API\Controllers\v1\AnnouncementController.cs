﻿using Asp.Versioning;
using Hsse.Application.IServices;
using Microsoft.AspNetCore.Mvc;

namespace Hsse.API.Controllers.v1
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class AnnouncementController : ControllerBase
    {
        private readonly IAnnouncementService _IAnnouncementService;
        private readonly ILogger<AnnouncementController> _logger;
        public AnnouncementController(IAnnouncementService announcementService, IConfiguration configuration, ILogger<AnnouncementController> logger)
        {
            _logger = logger;
            _IAnnouncementService = announcementService;
        }
    }
}
