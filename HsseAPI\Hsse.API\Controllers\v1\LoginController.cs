﻿using Asp.Versioning;
using Hsse.Application.IServices;
using Microsoft.AspNetCore.Mvc;
using System.Reflection;
using Hsse.Data.Dto.Response;
using Hsse.Infrastructure.Helpers;
using Microsoft.AspNetCore.Authorization;
using Azure;

namespace Hsse.API.Controllers.v1
{
    
    [ApiController]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class LoginController : ControllerBase
    {
        private readonly ILoginService _ILoginService;
        private readonly ILogger<LoginController> _logger;
        private readonly AuthorizeHelper _authorizeHelper;

        public LoginController(ILoginService loginService, AuthorizeHelper authorizeHelper, ILogger<LoginController> logger)
        {
            _logger = logger;
            _ILoginService = loginService;
            _authorizeHelper = authorizeHelper;

        }
        [AllowAnonymous]
        [HttpPost("ValidateUser")]
        public IActionResult ValidateUser(string? employeeEmail = null, string? employeeId = null, string? fCMToken = null)
        {
            List<EmployeeDetailsList> ListEmployeeDetailsData = new List<EmployeeDetailsList>();
            List<EmployeeDetails> EmployeeDetails = new List<EmployeeDetails>();
            List<EmployeeFacility> EmployeeFacility = new List<EmployeeFacility>();

            try
            {
                var objdata = new ResponseDetails();
                try
                {
                    // Validate user
                    var user = _ILoginService.ValidateUser(employeeEmail, employeeId, fCMToken);
                    var userfacility = _ILoginService.GetUserFacility(user.FirstOrDefault().EmailID);
                    var firstUser = user.FirstOrDefault();
                    if (firstUser != null)
                    {
                        var token = _authorizeHelper.GenerateToken(firstUser);
                        firstUser.Token = token;
                    }
                    ListEmployeeDetailsData.Add(new EmployeeDetailsList
                    {
                        EmployeeDetails = user.ToList(),
                        EmployeeFacility = userfacility.ToList(),
                    });


                    objdata.Status = 1;
                    objdata.Result = new
                    {
                        EmployeeDetailsData = ListEmployeeDetailsData,
                    };
                    objdata.Message = "User Details Fetched Successfully";
                }
                catch (Exception ex)
                {
                    objdata.Status = 0;
                    objdata.Result = null;
                    objdata.Message = "Not a Valid Email";
                }

                if (objdata.Result != null)
                {
                    return Ok(objdata);
                }
                else
                {
                    return Ok(objdata);
                }
            }
            catch (Exception ex)
            {
                var method = MethodBase.GetCurrentMethod()?.Name;
                _logger.LogError($"Method Name : {method ?? ""}, Error : {ex.Message}", ex.StackTrace);
                return BadRequest("Something went wrong,Please try again later.");
            }
        }
        [HttpPost("LogoutUser")]
        public async Task<IActionResult> Logout(int UserId)
        {
            var objdata = new ResponseDetails();
            try
            {
                var _userlogout = await _ILoginService.LogoutUser(UserId);
                if (_userlogout > 0)
                {
                    objdata.Status = 1;
                    objdata.Message = "User loggedout Successfully";
                    return Ok(objdata);
                }
                else
                {
                    objdata.Status = 0;
                    objdata.Message = "Error While logout";
                    return Ok(objdata);
                }
            }
            catch (Exception ex) { return Ok(ex.Message); }
        }
    }
}
