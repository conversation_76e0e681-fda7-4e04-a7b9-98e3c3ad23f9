﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Entities
{
    public class MstPosts
    {
        [Key]
        public int PostID { get; set; }
        public int UserID { get; set; }
        public int? FacilityID { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public string PostType { get; set; }
        public string Location { get; set; }
        public int? TaggedCategoryId { get; set; }
        public string RequiresFollowup { get; set; }
        public int? Status { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public int? ClosedBy { get; set; }
        public int? DeletedBy { get; set; }
        public bool IsDeleted { get; set; }
    }
}
