﻿using Hsse.Application.IServices;
using Hsse.Data.Dto.Response;
using Hsse.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Application.Services
{
    public class LoginService : ILoginService
    {
        private readonly ILoginRepository _ILoginRepository;
        public LoginService(ILoginRepository loginRepository)
        {
            _ILoginRepository = loginRepository;
        }
        public IEnumerable<EmployeeDetails> ValidateUser(string? employeeEmail, string? employeeId, string? fCMToken)
        {
            var result = _ILoginRepository.ValidateUser(employeeEmail, employeeId, fCMToken);
            return result;
        }
        public IEnumerable<EmployeeFacility> GetUserFacility(string? EmployeeEmail)
        {
            var result = _ILoginRepository.GetUserFacility(EmployeeEmail);
            return result;
        }
        public Task<int> LogoutUser(int userId)
        {
            var result = _ILoginRepository.LogoutUser(userId);
            return result;
        }
    }
}
