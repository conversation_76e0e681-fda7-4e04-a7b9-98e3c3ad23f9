﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Entities
{
    public class MstInspections
    {
        [Key]
        public int InspectionId { get; set; }
        public int? FacilityID { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public DateTime InspectionDate { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? ModifiedAt { get; set; }
        public string ReferenceNo { get; set; }
    }
}
