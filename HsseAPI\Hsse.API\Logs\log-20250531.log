2025-05-31 10:14:43.099 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-31 10:14:44.011 +05:30 [INF] Now listening on: https://localhost:7231
2025-05-31 10:14:44.063 +05:30 [INF] Now listening on: http://localhost:5133
2025-05-31 10:14:44.304 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-31 10:14:44.334 +05:30 [INF] Hosting environment: Development
2025-05-31 10:14:44.432 +05:30 [INF] Content root path: D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.API
2025-05-31 10:14:48.087 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-05-31 10:14:48.536 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 463.393ms
2025-05-31 10:14:48.556 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-05-31 10:14:48.558 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-05-31 10:14:48.587 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 28.6005ms
2025-05-31 10:14:48.671 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 112.6245ms
2025-05-31 10:14:49.279 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-05-31 10:14:49.373 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 94.2925ms
2025-05-31 10:59:11.704 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-31 10:59:12.126 +05:30 [INF] Now listening on: https://localhost:7231
2025-05-31 10:59:12.128 +05:30 [INF] Now listening on: http://localhost:5133
2025-05-31 10:59:12.257 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-31 10:59:12.267 +05:30 [INF] Hosting environment: Development
2025-05-31 10:59:12.284 +05:30 [INF] Content root path: D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.API
2025-05-31 10:59:12.813 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-05-31 10:59:13.153 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-05-31 10:59:13.153 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-05-31 10:59:13.161 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 362.7589ms
2025-05-31 10:59:13.177 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 23.5563ms
2025-05-31 10:59:13.243 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 89.2726ms
2025-05-31 10:59:13.737 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-05-31 10:59:13.795 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 58.4838ms
2025-05-31 11:02:15.563 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-31 11:02:16.121 +05:30 [INF] Now listening on: https://localhost:7231
2025-05-31 11:02:16.131 +05:30 [INF] Now listening on: http://localhost:5133
2025-05-31 11:02:16.592 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-31 11:02:16.680 +05:30 [INF] Hosting environment: Development
2025-05-31 11:02:16.692 +05:30 [INF] Content root path: D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.API
2025-05-31 11:02:16.755 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-05-31 11:02:17.121 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 378.9745ms
2025-05-31 11:02:17.139 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-05-31 11:02:17.154 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-05-31 11:02:17.170 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 16.5554ms
2025-05-31 11:02:17.252 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 113.8507ms
2025-05-31 11:02:17.918 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-05-31 11:02:18.001 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 83.2431ms
2025-05-31 11:02:26.397 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Post - null null
2025-05-31 11:02:27.769 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.PostController.GetAll (Hsse.API)'
2025-05-31 11:02:27.801 +05:30 [INF] Route matched with {action = "GetAll", controller = "Post"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetAll() on controller Hsse.API.Controllers.v1.PostController (Hsse.API).
2025-05-31 11:02:28.223 +05:30 [INF] Executing StatusCodeResult, setting HTTP status code 200
2025-05-31 11:02:28.232 +05:30 [INF] Executed action Hsse.API.Controllers.v1.PostController.GetAll (Hsse.API) in 421.3627ms
2025-05-31 11:02:28.234 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.PostController.GetAll (Hsse.API)'
2025-05-31 11:02:28.236 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Post - 200 0 null 1838.8612ms
2025-05-31 11:09:53.759 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-31 11:09:54.061 +05:30 [INF] Now listening on: https://localhost:7231
2025-05-31 11:09:54.064 +05:30 [INF] Now listening on: http://localhost:5133
2025-05-31 11:09:54.158 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-31 11:09:54.160 +05:30 [INF] Hosting environment: Development
2025-05-31 11:09:54.162 +05:30 [INF] Content root path: D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.API
2025-05-31 11:09:56.048 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-05-31 11:09:56.468 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-05-31 11:09:56.474 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 434.0949ms
2025-05-31 11:09:56.469 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-05-31 11:09:56.500 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 31.8645ms
2025-05-31 11:09:56.584 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 115.9486ms
2025-05-31 11:09:57.229 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-05-31 11:09:57.278 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 49.7751ms
2025-05-31 15:20:02.526 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-31 15:20:02.991 +05:30 [INF] Now listening on: https://localhost:7231
2025-05-31 15:20:02.995 +05:30 [INF] Now listening on: http://localhost:5133
2025-05-31 15:20:03.140 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-31 15:20:03.147 +05:30 [INF] Hosting environment: Development
2025-05-31 15:20:03.148 +05:30 [INF] Content root path: D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.API
2025-05-31 15:20:03.937 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-05-31 15:20:04.270 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 350.7115ms
2025-05-31 15:20:04.291 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-05-31 15:20:04.293 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-05-31 15:20:04.337 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 45.9554ms
2025-05-31 15:20:04.393 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 101.4355ms
2025-05-31 15:20:04.990 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-05-31 15:20:05.008 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 18.545ms
2025-05-31 15:20:10.662 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Post - null null
2025-05-31 15:20:12.076 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:20:12.116 +05:30 [INF] Route matched with {action = "GetPosts", controller = "Post"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetPosts(System.Nullable`1[System.Int32]) on controller Hsse.API.Controllers.v1.PostController (Hsse.API).
2025-05-31 15:20:13.678 +05:30 [INF] Executed action Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API) in 1554.154ms
2025-05-31 15:20:13.684 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:20:13.699 +05:30 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: The entity type 'MstActionParty' requires a primary key to be defined. If you intended to use a keyless entity type, call 'HasNoKey' in 'OnModelCreating'. For more information on keyless entity types, see https://go.microsoft.com/fwlink/?linkid=2141943.
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelValidator.ValidateNonNullPrimaryKeys(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelValidator.Validate(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.Infrastructure.RelationalModelValidator.Validate(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.SqlServer.Infrastructure.Internal.SqlServerModelValidator.Validate(IModel model, IDiagnosticsLogger`1 logger)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelRuntimeInitializer.Initialize(IModel model, Boolean designTime, IDiagnosticsLogger`1 validationLogger)
   at Microsoft.EntityFrameworkCore.Infrastructure.ModelSource.GetModel(DbContext context, ModelCreationDependencies modelCreationDependencies, Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.CreateModel(Boolean designTime)
   at Microsoft.EntityFrameworkCore.Internal.DbContextServices.get_Model()
   at Microsoft.EntityFrameworkCore.Infrastructure.EntityFrameworkServicesBuilder.<>c.<TryAddCoreServices>b__8_4(IServiceProvider p)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSiteMain(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitCache(ServiceCallSite callSite, RuntimeResolverContext context, ServiceProviderEngineScope serviceProviderEngine, RuntimeResolverLock lockType)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.VisitScopeCache(ServiceCallSite callSite, RuntimeResolverContext context)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteVisitor`2.VisitCallSite(ServiceCallSite callSite, TArgument argument)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteRuntimeResolver.Resolve(ServiceCallSite callSite, ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.DynamicServiceProviderEngine.<>c__DisplayClass2_0.<RealizeService>b__0(ServiceProviderEngineScope scope)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService(IServiceProvider provider, Type serviceType)
   at Microsoft.Extensions.DependencyInjection.ServiceProviderServiceExtensions.GetRequiredService[T](IServiceProvider provider)
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.get_ContextServices()
   at Microsoft.EntityFrameworkCore.DbContext.get_Model()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.get_EntityType()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.CheckState()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.get_EntityQueryable()
   at Microsoft.EntityFrameworkCore.Internal.InternalDbSet`1.System.Linq.IQueryable.get_Provider()
   at System.Linq.Queryable.Where[TSource](IQueryable`1 source, Expression`1 predicate)
   at Hsse.Infrastructure.Repositories.PostRepository.GetPosts(Nullable`1 id) in D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.Infrastructure\Repositories\PostRepository.cs:line 116
   at Hsse.Application.Services.PostService.GetPosts(Nullable`1 id) in D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.Application\Services\PostService.cs:line 27
   at lambda_method2(Closure, Object, Object[])
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-05-31 15:20:13.828 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Post - 500 null text/plain; charset=utf-8 3165.9572ms
2025-05-31 15:24:30.467 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-31 15:24:30.922 +05:30 [INF] Now listening on: https://localhost:7231
2025-05-31 15:24:30.925 +05:30 [INF] Now listening on: http://localhost:5133
2025-05-31 15:24:31.032 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-31 15:24:31.039 +05:30 [INF] Hosting environment: Development
2025-05-31 15:24:31.042 +05:30 [INF] Content root path: D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.API
2025-05-31 15:24:32.711 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-05-31 15:24:33.345 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 644.319ms
2025-05-31 15:24:33.371 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-05-31 15:24:33.374 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-05-31 15:24:33.409 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 38.0178ms
2025-05-31 15:24:33.494 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 120.1171ms
2025-05-31 15:24:34.088 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-05-31 15:24:34.136 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 47.4383ms
2025-05-31 15:24:41.061 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Post - null null
2025-05-31 15:24:41.164 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:24:41.211 +05:30 [INF] Route matched with {action = "GetPosts", controller = "Post"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetPosts(System.Nullable`1[System.Int32]) on controller Hsse.API.Controllers.v1.PostController (Hsse.API).
2025-05-31 15:24:43.541 +05:30 [ERR] An error occurred using the connection to database '' on server ''.
2025-05-31 15:24:43.569 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'Hsse.Data.Helper.MasterDBContext'.
System.InvalidOperationException: The ConnectionString property has not been initialized.
   at Microsoft.Data.SqlClient.SqlConnection.PermissionDemand()
   at Microsoft.Data.SqlClient.SqlConnectionFactory.PermissionDemand(DbConnection outerConnection)
   at Microsoft.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at Microsoft.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at Microsoft.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry, SqlConnectionOverrides overrides)
   at Microsoft.Data.SqlClient.SqlConnection.Open(SqlConnectionOverrides overrides)
   at Microsoft.Data.SqlClient.SqlConnection.Open()
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerConnection.OpenDbConnection(Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternal(Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.Open(Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
System.InvalidOperationException: The ConnectionString property has not been initialized.
   at Microsoft.Data.SqlClient.SqlConnection.PermissionDemand()
   at Microsoft.Data.SqlClient.SqlConnectionFactory.PermissionDemand(DbConnection outerConnection)
   at Microsoft.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at Microsoft.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at Microsoft.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry, SqlConnectionOverrides overrides)
   at Microsoft.Data.SqlClient.SqlConnection.Open(SqlConnectionOverrides overrides)
   at Microsoft.Data.SqlClient.SqlConnection.Open()
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerConnection.OpenDbConnection(Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternal(Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.Open(Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
2025-05-31 15:24:43.602 +05:30 [INF] Executed action Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API) in 2379.855ms
2025-05-31 15:24:43.606 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:24:43.615 +05:30 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: The ConnectionString property has not been initialized.
   at Microsoft.Data.SqlClient.SqlConnection.PermissionDemand()
   at Microsoft.Data.SqlClient.SqlConnectionFactory.PermissionDemand(DbConnection outerConnection)
   at Microsoft.Data.ProviderBase.DbConnectionInternal.TryOpenConnectionInternal(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at Microsoft.Data.ProviderBase.DbConnectionClosed.TryOpenConnection(DbConnection outerConnection, DbConnectionFactory connectionFactory, TaskCompletionSource`1 retry, DbConnectionOptions userOptions)
   at Microsoft.Data.SqlClient.SqlConnection.TryOpen(TaskCompletionSource`1 retry, SqlConnectionOverrides overrides)
   at Microsoft.Data.SqlClient.SqlConnection.Open(SqlConnectionOverrides overrides)
   at Microsoft.Data.SqlClient.SqlConnection.Open()
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerConnection.OpenDbConnection(Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternal(Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.Open(Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Hsse.Infrastructure.Repositories.PostRepository.GetPosts(Nullable`1 id) in D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.Infrastructure\Repositories\PostRepository.cs:line 116
   at Hsse.Application.Services.PostService.GetPosts(Nullable`1 id) in D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.Application\Services\PostService.cs:line 27
   at lambda_method2(Closure, Object, Object[])
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-05-31 15:24:43.724 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Post - 500 null text/plain; charset=utf-8 2664.0391ms
2025-05-31 15:26:31.213 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-31 15:26:31.560 +05:30 [INF] Now listening on: https://localhost:7231
2025-05-31 15:26:31.564 +05:30 [INF] Now listening on: http://localhost:5133
2025-05-31 15:26:31.733 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-31 15:26:31.737 +05:30 [INF] Hosting environment: Development
2025-05-31 15:26:31.738 +05:30 [INF] Content root path: D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.API
2025-05-31 15:26:33.221 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-05-31 15:26:33.701 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 496.4224ms
2025-05-31 15:26:33.734 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-05-31 15:26:33.741 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-05-31 15:26:33.784 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 50.2928ms
2025-05-31 15:26:33.893 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 151.9577ms
2025-05-31 15:26:34.584 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-05-31 15:26:34.659 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 75.0852ms
2025-05-31 15:26:41.105 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Post?id=1 - null null
2025-05-31 15:26:41.201 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:26:41.247 +05:30 [INF] Route matched with {action = "GetPosts", controller = "Post"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetPosts(System.Nullable`1[System.Int32]) on controller Hsse.API.Controllers.v1.PostController (Hsse.API).
2025-05-31 15:26:43.448 +05:30 [INF] Executed DbCommand (209ms) [Parameters=[@__id_Value_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [m].[PostID], [m].[ClosedBy], [m].[CreatedAt], [m].[DeletedBy], [m].[Description], [m].[FacilityID], [m].[IsDeleted], [m].[Location], [m].[PostType], [m].[RequiresFollowup], [m].[Status], [m].[TaggedCategoryId], [m].[Title], [m].[UpdatedAt], [m].[UserID]
FROM [MstPosts] AS [m]
WHERE [m].[PostID] = @__id_Value_0 AND [m].[IsDeleted] = CAST(0 AS bit)
2025-05-31 15:26:43.483 +05:30 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[Hsse.Data.Entities.MstPosts, Hsse.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-31 15:26:43.502 +05:30 [INF] Executed action Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API) in 2248.1544ms
2025-05-31 15:26:43.504 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:26:43.518 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Post?id=1 - 200 null application/json; charset=utf-8 2412.7507ms
2025-05-31 15:26:48.181 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Post - null null
2025-05-31 15:26:48.197 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:26:48.202 +05:30 [INF] Route matched with {action = "GetPosts", controller = "Post"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetPosts(System.Nullable`1[System.Int32]) on controller Hsse.API.Controllers.v1.PostController (Hsse.API).
2025-05-31 15:26:48.279 +05:30 [INF] Executed DbCommand (41ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[PostID], [m].[ClosedBy], [m].[CreatedAt], [m].[DeletedBy], [m].[Description], [m].[FacilityID], [m].[IsDeleted], [m].[Location], [m].[PostType], [m].[RequiresFollowup], [m].[Status], [m].[TaggedCategoryId], [m].[Title], [m].[UpdatedAt], [m].[UserID]
FROM [MstPosts] AS [m]
WHERE [m].[IsDeleted] = CAST(0 AS bit)
2025-05-31 15:26:48.340 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'Hsse.Data.Helper.MasterDBContext'.
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlBuffer.ThrowIfNull()
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method40(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlBuffer.ThrowIfNull()
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method40(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
2025-05-31 15:26:48.350 +05:30 [INF] Executed action Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API) in 143.7541ms
2025-05-31 15:26:48.353 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:26:48.365 +05:30 [ERR] An unhandled exception has occurred while executing the request.
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlBuffer.ThrowIfNull()
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method40(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Hsse.Infrastructure.Repositories.PostRepository.GetPosts(Nullable`1 id) in D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.Infrastructure\Repositories\PostRepository.cs:line 116
   at Hsse.Application.Services.PostService.GetPosts(Nullable`1 id) in D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.Application\Services\PostService.cs:line 27
   at lambda_method2(Closure, Object, Object[])
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-05-31 15:26:48.402 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Post - 500 null text/plain; charset=utf-8 221.0221ms
2025-05-31 15:26:54.477 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Post - null null
2025-05-31 15:26:54.484 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:26:54.486 +05:30 [INF] Route matched with {action = "GetPosts", controller = "Post"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetPosts(System.Nullable`1[System.Int32]) on controller Hsse.API.Controllers.v1.PostController (Hsse.API).
2025-05-31 15:26:54.625 +05:30 [INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[PostID], [m].[ClosedBy], [m].[CreatedAt], [m].[DeletedBy], [m].[Description], [m].[FacilityID], [m].[IsDeleted], [m].[Location], [m].[PostType], [m].[RequiresFollowup], [m].[Status], [m].[TaggedCategoryId], [m].[Title], [m].[UpdatedAt], [m].[UserID]
FROM [MstPosts] AS [m]
WHERE [m].[IsDeleted] = CAST(0 AS bit)
2025-05-31 15:26:54.640 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'Hsse.Data.Helper.MasterDBContext'.
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlBuffer.ThrowIfNull()
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method40(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlBuffer.ThrowIfNull()
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method40(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
2025-05-31 15:26:54.653 +05:30 [INF] Executed action Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API) in 163.7184ms
2025-05-31 15:26:54.657 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:26:54.663 +05:30 [ERR] An unhandled exception has occurred while executing the request.
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlBuffer.ThrowIfNull()
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method40(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Hsse.Infrastructure.Repositories.PostRepository.GetPosts(Nullable`1 id) in D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.Infrastructure\Repositories\PostRepository.cs:line 116
   at Hsse.Application.Services.PostService.GetPosts(Nullable`1 id) in D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.Application\Services\PostService.cs:line 27
   at lambda_method2(Closure, Object, Object[])
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-05-31 15:26:54.682 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Post - 500 null text/plain; charset=utf-8 205.3355ms
2025-05-31 15:27:04.579 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Post?id=1 - null null
2025-05-31 15:27:04.587 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:27:04.589 +05:30 [INF] Route matched with {action = "GetPosts", controller = "Post"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetPosts(System.Nullable`1[System.Int32]) on controller Hsse.API.Controllers.v1.PostController (Hsse.API).
2025-05-31 15:27:04.633 +05:30 [INF] Executed DbCommand (36ms) [Parameters=[@__id_Value_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [m].[PostID], [m].[ClosedBy], [m].[CreatedAt], [m].[DeletedBy], [m].[Description], [m].[FacilityID], [m].[IsDeleted], [m].[Location], [m].[PostType], [m].[RequiresFollowup], [m].[Status], [m].[TaggedCategoryId], [m].[Title], [m].[UpdatedAt], [m].[UserID]
FROM [MstPosts] AS [m]
WHERE [m].[PostID] = @__id_Value_0 AND [m].[IsDeleted] = CAST(0 AS bit)
2025-05-31 15:27:04.640 +05:30 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[Hsse.Data.Entities.MstPosts, Hsse.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-31 15:27:04.645 +05:30 [INF] Executed action Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API) in 53.9203ms
2025-05-31 15:27:04.650 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:27:04.653 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Post?id=1 - 200 null application/json; charset=utf-8 74.2003ms
2025-05-31 15:27:08.866 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Post?id=2 - null null
2025-05-31 15:27:08.873 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:27:08.874 +05:30 [INF] Route matched with {action = "GetPosts", controller = "Post"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetPosts(System.Nullable`1[System.Int32]) on controller Hsse.API.Controllers.v1.PostController (Hsse.API).
2025-05-31 15:27:08.951 +05:30 [INF] Executed DbCommand (67ms) [Parameters=[@__id_Value_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [m].[PostID], [m].[ClosedBy], [m].[CreatedAt], [m].[DeletedBy], [m].[Description], [m].[FacilityID], [m].[IsDeleted], [m].[Location], [m].[PostType], [m].[RequiresFollowup], [m].[Status], [m].[TaggedCategoryId], [m].[Title], [m].[UpdatedAt], [m].[UserID]
FROM [MstPosts] AS [m]
WHERE [m].[PostID] = @__id_Value_0 AND [m].[IsDeleted] = CAST(0 AS bit)
2025-05-31 15:27:08.961 +05:30 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[Hsse.Data.Entities.MstPosts, Hsse.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-31 15:27:08.963 +05:30 [INF] Executed action Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API) in 81.7628ms
2025-05-31 15:27:08.965 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:27:08.967 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Post?id=2 - 200 null application/json; charset=utf-8 100.4764ms
2025-05-31 15:27:13.562 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Post?id=3 - null null
2025-05-31 15:27:13.568 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:27:13.570 +05:30 [INF] Route matched with {action = "GetPosts", controller = "Post"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetPosts(System.Nullable`1[System.Int32]) on controller Hsse.API.Controllers.v1.PostController (Hsse.API).
2025-05-31 15:27:13.616 +05:30 [INF] Executed DbCommand (35ms) [Parameters=[@__id_Value_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [m].[PostID], [m].[ClosedBy], [m].[CreatedAt], [m].[DeletedBy], [m].[Description], [m].[FacilityID], [m].[IsDeleted], [m].[Location], [m].[PostType], [m].[RequiresFollowup], [m].[Status], [m].[TaggedCategoryId], [m].[Title], [m].[UpdatedAt], [m].[UserID]
FROM [MstPosts] AS [m]
WHERE [m].[PostID] = @__id_Value_0 AND [m].[IsDeleted] = CAST(0 AS bit)
2025-05-31 15:27:13.621 +05:30 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[Hsse.Data.Entities.MstPosts, Hsse.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-31 15:27:13.625 +05:30 [INF] Executed action Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API) in 52.115ms
2025-05-31 15:27:13.631 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:27:13.633 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Post?id=3 - 200 null application/json; charset=utf-8 71.336ms
2025-05-31 15:27:37.828 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Post?id=3 - null null
2025-05-31 15:27:37.831 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:27:37.833 +05:30 [INF] Route matched with {action = "GetPosts", controller = "Post"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetPosts(System.Nullable`1[System.Int32]) on controller Hsse.API.Controllers.v1.PostController (Hsse.API).
2025-05-31 15:27:37.876 +05:30 [INF] Executed DbCommand (41ms) [Parameters=[@__id_Value_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [m].[PostID], [m].[ClosedBy], [m].[CreatedAt], [m].[DeletedBy], [m].[Description], [m].[FacilityID], [m].[IsDeleted], [m].[Location], [m].[PostType], [m].[RequiresFollowup], [m].[Status], [m].[TaggedCategoryId], [m].[Title], [m].[UpdatedAt], [m].[UserID]
FROM [MstPosts] AS [m]
WHERE [m].[PostID] = @__id_Value_0 AND [m].[IsDeleted] = CAST(0 AS bit)
2025-05-31 15:27:37.882 +05:30 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[Hsse.Data.Entities.MstPosts, Hsse.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-31 15:27:37.885 +05:30 [INF] Executed action Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API) in 50.4777ms
2025-05-31 15:27:37.887 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:27:37.889 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Post?id=3 - 200 null application/json; charset=utf-8 61.4631ms
2025-05-31 15:27:38.964 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Post?id=3 - null null
2025-05-31 15:27:38.970 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:27:38.977 +05:30 [INF] Route matched with {action = "GetPosts", controller = "Post"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetPosts(System.Nullable`1[System.Int32]) on controller Hsse.API.Controllers.v1.PostController (Hsse.API).
2025-05-31 15:27:39.018 +05:30 [INF] Executed DbCommand (36ms) [Parameters=[@__id_Value_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [m].[PostID], [m].[ClosedBy], [m].[CreatedAt], [m].[DeletedBy], [m].[Description], [m].[FacilityID], [m].[IsDeleted], [m].[Location], [m].[PostType], [m].[RequiresFollowup], [m].[Status], [m].[TaggedCategoryId], [m].[Title], [m].[UpdatedAt], [m].[UserID]
FROM [MstPosts] AS [m]
WHERE [m].[PostID] = @__id_Value_0 AND [m].[IsDeleted] = CAST(0 AS bit)
2025-05-31 15:27:39.022 +05:30 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[Hsse.Data.Entities.MstPosts, Hsse.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-31 15:27:39.025 +05:30 [INF] Executed action Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API) in 45.4098ms
2025-05-31 15:27:39.028 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:27:39.029 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Post?id=3 - 200 null application/json; charset=utf-8 65.5887ms
2025-05-31 15:27:40.201 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Post?id=3 - null null
2025-05-31 15:27:40.210 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:27:40.214 +05:30 [INF] Route matched with {action = "GetPosts", controller = "Post"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetPosts(System.Nullable`1[System.Int32]) on controller Hsse.API.Controllers.v1.PostController (Hsse.API).
2025-05-31 15:27:40.254 +05:30 [INF] Executed DbCommand (35ms) [Parameters=[@__id_Value_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [m].[PostID], [m].[ClosedBy], [m].[CreatedAt], [m].[DeletedBy], [m].[Description], [m].[FacilityID], [m].[IsDeleted], [m].[Location], [m].[PostType], [m].[RequiresFollowup], [m].[Status], [m].[TaggedCategoryId], [m].[Title], [m].[UpdatedAt], [m].[UserID]
FROM [MstPosts] AS [m]
WHERE [m].[PostID] = @__id_Value_0 AND [m].[IsDeleted] = CAST(0 AS bit)
2025-05-31 15:27:40.261 +05:30 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[Hsse.Data.Entities.MstPosts, Hsse.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-31 15:27:40.263 +05:30 [INF] Executed action Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API) in 46.0438ms
2025-05-31 15:27:40.267 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:27:40.268 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Post?id=3 - 200 null application/json; charset=utf-8 67.5581ms
2025-05-31 15:27:46.148 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Post - null null
2025-05-31 15:27:46.159 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:27:46.161 +05:30 [INF] Route matched with {action = "GetPosts", controller = "Post"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetPosts(System.Nullable`1[System.Int32]) on controller Hsse.API.Controllers.v1.PostController (Hsse.API).
2025-05-31 15:27:46.205 +05:30 [INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[PostID], [m].[ClosedBy], [m].[CreatedAt], [m].[DeletedBy], [m].[Description], [m].[FacilityID], [m].[IsDeleted], [m].[Location], [m].[PostType], [m].[RequiresFollowup], [m].[Status], [m].[TaggedCategoryId], [m].[Title], [m].[UpdatedAt], [m].[UserID]
FROM [MstPosts] AS [m]
WHERE [m].[IsDeleted] = CAST(0 AS bit)
2025-05-31 15:27:46.212 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'Hsse.Data.Helper.MasterDBContext'.
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlBuffer.ThrowIfNull()
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method40(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlBuffer.ThrowIfNull()
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method40(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
2025-05-31 15:27:46.222 +05:30 [INF] Executed action Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API) in 56.8505ms
2025-05-31 15:27:46.227 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:27:46.231 +05:30 [ERR] An unhandled exception has occurred while executing the request.
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlBuffer.ThrowIfNull()
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method40(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Hsse.Infrastructure.Repositories.PostRepository.GetPosts(Nullable`1 id) in D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.Infrastructure\Repositories\PostRepository.cs:line 116
   at Hsse.Application.Services.PostService.GetPosts(Nullable`1 id) in D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.Application\Services\PostService.cs:line 27
   at lambda_method2(Closure, Object, Object[])
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-05-31 15:27:46.247 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Post - 500 null text/plain; charset=utf-8 99.5566ms
2025-05-31 15:29:16.323 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-31 15:29:16.850 +05:30 [INF] Now listening on: https://localhost:7231
2025-05-31 15:29:16.853 +05:30 [INF] Now listening on: http://localhost:5133
2025-05-31 15:29:17.096 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-31 15:29:17.100 +05:30 [INF] Hosting environment: Development
2025-05-31 15:29:17.101 +05:30 [INF] Content root path: D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.API
2025-05-31 15:29:19.047 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-05-31 15:29:19.495 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 466.7261ms
2025-05-31 15:29:19.537 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-05-31 15:29:19.537 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-05-31 15:29:19.560 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 22.4659ms
2025-05-31 15:29:19.648 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 110.7377ms
2025-05-31 15:29:20.805 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-05-31 15:29:20.819 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 14.3086ms
2025-05-31 15:29:40.481 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Post - null null
2025-05-31 15:29:40.589 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:29:40.636 +05:30 [INF] Route matched with {action = "GetPosts", controller = "Post"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetPosts() on controller Hsse.API.Controllers.v1.PostController (Hsse.API).
2025-05-31 15:29:42.619 +05:30 [INF] Executed DbCommand (67ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[PostID], [m].[ClosedBy], [m].[CreatedAt], [m].[DeletedBy], [m].[Description], [m].[FacilityID], [m].[IsDeleted], [m].[Location], [m].[PostType], [m].[RequiresFollowup], [m].[Status], [m].[TaggedCategoryId], [m].[Title], [m].[UpdatedAt], [m].[UserID]
FROM [MstPosts] AS [m]
WHERE [m].[IsDeleted] = CAST(0 AS bit)
2025-05-31 15:29:42.673 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'Hsse.Data.Helper.MasterDBContext'.
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlBuffer.ThrowIfNull()
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method37(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlBuffer.ThrowIfNull()
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method37(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
2025-05-31 15:29:42.701 +05:30 [INF] Executed action Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API) in 2057.145ms
2025-05-31 15:29:42.705 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:29:42.712 +05:30 [ERR] An unhandled exception has occurred while executing the request.
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlBuffer.ThrowIfNull()
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method37(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Hsse.Infrastructure.Repositories.PostRepository.GetPosts() in D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.Infrastructure\Repositories\PostRepository.cs:line 107
   at Hsse.Application.Services.PostService.GetPosts() in D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.Application\Services\PostService.cs:line 27
   at lambda_method2(Closure, Object, Object[])
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-05-31 15:29:42.771 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Post - 500 null text/plain; charset=utf-8 2290.6394ms
2025-05-31 15:31:08.938 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-31 15:31:09.454 +05:30 [INF] Now listening on: https://localhost:7231
2025-05-31 15:31:09.467 +05:30 [INF] Now listening on: http://localhost:5133
2025-05-31 15:31:09.673 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-31 15:31:09.676 +05:30 [INF] Hosting environment: Development
2025-05-31 15:31:09.679 +05:30 [INF] Content root path: D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.API
2025-05-31 15:31:11.290 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-05-31 15:31:11.702 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-05-31 15:31:11.702 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-05-31 15:31:11.706 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 429.7377ms
2025-05-31 15:31:11.726 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 23.8508ms
2025-05-31 15:31:11.820 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 117.5025ms
2025-05-31 15:31:12.535 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-05-31 15:31:12.582 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 47.8276ms
2025-05-31 15:31:23.631 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Post - null null
2025-05-31 15:31:23.734 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:31:23.777 +05:30 [INF] Route matched with {action = "GetPosts", controller = "Post"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetPosts() on controller Hsse.API.Controllers.v1.PostController (Hsse.API).
2025-05-31 15:31:25.656 +05:30 [INF] Executed DbCommand (73ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[PostID], [m].[ClosedBy], [m].[CreatedAt], [m].[DeletedBy], [m].[Description], [m].[FacilityID], [m].[IsDeleted], [m].[Location], [m].[PostType], [m].[RequiresFollowup], [m].[Status], [m].[TaggedCategoryId], [m].[Title], [m].[UpdatedAt], [m].[UserID]
FROM [MstPosts] AS [m]
WHERE [m].[IsDeleted] = CAST(0 AS bit)
2025-05-31 15:31:25.696 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'Hsse.Data.Helper.MasterDBContext'.
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlBuffer.ThrowIfNull()
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method37(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlBuffer.ThrowIfNull()
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method37(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
2025-05-31 15:31:25.730 +05:30 [INF] Executed action Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API) in 1939.4047ms
2025-05-31 15:31:25.735 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:31:25.744 +05:30 [ERR] An unhandled exception has occurred while executing the request.
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlBuffer.ThrowIfNull()
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method37(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Hsse.Infrastructure.Repositories.PostRepository.GetPosts() in D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.Infrastructure\Repositories\PostRepository.cs:line 107
   at Hsse.Application.Services.PostService.GetPosts() in D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.Application\Services\PostService.cs:line 27
   at lambda_method2(Closure, Object, Object[])
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-05-31 15:31:25.786 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Post - 500 null text/plain; charset=utf-8 2154.6858ms
2025-05-31 15:38:14.133 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-31 15:38:14.598 +05:30 [INF] Now listening on: https://localhost:7231
2025-05-31 15:38:14.602 +05:30 [INF] Now listening on: http://localhost:5133
2025-05-31 15:38:14.737 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-31 15:38:14.740 +05:30 [INF] Hosting environment: Development
2025-05-31 15:38:14.741 +05:30 [INF] Content root path: D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.API
2025-05-31 15:38:16.552 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-05-31 15:38:17.046 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 507.5652ms
2025-05-31 15:38:17.106 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-05-31 15:38:17.137 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-05-31 15:38:17.256 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 150.6309ms
2025-05-31 15:38:17.361 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 224.4768ms
2025-05-31 15:38:18.002 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-05-31 15:38:18.037 +05:30 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Conflicting method/path combination "GET api/v{version}/Post" for actions - Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API),Hsse.API.Controllers.v1.PostController.GetPostCategories (Hsse.API). Actions require a unique method/path combination for Swagger/OpenAPI 3.0. Use ConflictingActionsResolver as a workaround
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperations(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePaths(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerDocumentWithoutFilters(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-05-31 15:38:18.051 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 49.5251ms
2025-05-31 15:39:08.904 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-31 15:39:09.421 +05:30 [INF] Now listening on: https://localhost:7231
2025-05-31 15:39:09.425 +05:30 [INF] Now listening on: http://localhost:5133
2025-05-31 15:39:09.545 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-31 15:39:09.549 +05:30 [INF] Hosting environment: Development
2025-05-31 15:39:09.550 +05:30 [INF] Content root path: D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.API
2025-05-31 15:39:11.138 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-05-31 15:39:11.535 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 412.9205ms
2025-05-31 15:39:11.566 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-05-31 15:39:11.566 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-05-31 15:39:11.737 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 171.7148ms
2025-05-31 15:39:11.825 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 259.1191ms
2025-05-31 15:39:12.498 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-05-31 15:39:12.521 +05:30 [ERR] An unhandled exception has occurred while executing the request.
Swashbuckle.AspNetCore.SwaggerGen.SwaggerGeneratorException: Conflicting method/path combination "GET api/v{version}/Post" for actions - Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API),Hsse.API.Controllers.v1.PostController.GetCategories (Hsse.API). Actions require a unique method/path combination for Swagger/OpenAPI 3.0. Use ConflictingActionsResolver as a workaround
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GenerateOperations(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GeneratePaths(IEnumerable`1 apiDescriptions, SchemaRepository schemaRepository)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerDocumentWithoutFilters(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.SwaggerGen.SwaggerGenerator.GetSwaggerAsync(String documentName, String host, String basePath)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-05-31 15:39:12.552 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 500 null text/plain; charset=utf-8 54.734ms
2025-05-31 15:40:09.702 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-31 15:40:10.181 +05:30 [INF] Now listening on: https://localhost:7231
2025-05-31 15:40:10.183 +05:30 [INF] Now listening on: http://localhost:5133
2025-05-31 15:40:10.268 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-31 15:40:10.273 +05:30 [INF] Hosting environment: Development
2025-05-31 15:40:10.276 +05:30 [INF] Content root path: D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.API
2025-05-31 15:40:11.985 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-05-31 15:40:12.417 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 447.3983ms
2025-05-31 15:40:12.478 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-05-31 15:40:12.461 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-05-31 15:40:12.510 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 48.8905ms
2025-05-31 15:40:12.679 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 200.6563ms
2025-05-31 15:40:13.256 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-05-31 15:40:13.316 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 59.9415ms
2025-05-31 15:42:14.269 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-31 15:42:14.772 +05:30 [INF] Now listening on: https://localhost:7231
2025-05-31 15:42:14.776 +05:30 [INF] Now listening on: http://localhost:5133
2025-05-31 15:42:15.120 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-31 15:42:15.122 +05:30 [INF] Hosting environment: Development
2025-05-31 15:42:15.123 +05:30 [INF] Content root path: D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.API
2025-05-31 15:42:16.792 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-05-31 15:42:17.182 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 402.6713ms
2025-05-31 15:42:17.277 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-05-31 15:42:17.290 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-05-31 15:42:17.309 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 31.4894ms
2025-05-31 15:42:17.383 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 93.4024ms
2025-05-31 15:42:18.015 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-05-31 15:42:18.046 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 37.4037ms
2025-05-31 15:42:23.716 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Post/GetPostCategories - null null
2025-05-31 15:42:23.851 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.PostController.GetPostCategories (Hsse.API)'
2025-05-31 15:42:23.876 +05:30 [INF] Route matched with {action = "GetPostCategories", controller = "Post"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetPostCategories() on controller Hsse.API.Controllers.v1.PostController (Hsse.API).
2025-05-31 15:42:25.865 +05:30 [INF] Executed DbCommand (63ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[CatID], [m].[CategoryName], [m].[CreatedAt]
FROM [MstPostCategories] AS [m]
2025-05-31 15:42:25.976 +05:30 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[Hsse.Data.Entities.MstPostCategories, Hsse.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-31 15:42:26.002 +05:30 [INF] Executed action Hsse.API.Controllers.v1.PostController.GetPostCategories (Hsse.API) in 2116.8238ms
2025-05-31 15:42:26.004 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.PostController.GetPostCategories (Hsse.API)'
2025-05-31 15:42:26.021 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Post/GetPostCategories - 200 null application/json; charset=utf-8 2304.4465ms
2025-05-31 15:43:38.705 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Post/GetPostCategories - null null
2025-05-31 15:43:38.716 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.PostController.GetPostCategories (Hsse.API)'
2025-05-31 15:43:38.721 +05:30 [INF] Route matched with {action = "GetPostCategories", controller = "Post"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetPostCategories() on controller Hsse.API.Controllers.v1.PostController (Hsse.API).
2025-05-31 15:43:38.838 +05:30 [INF] Executed DbCommand (80ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[CatID], [m].[CategoryName], [m].[CreatedAt]
FROM [MstPostCategories] AS [m]
2025-05-31 15:43:38.850 +05:30 [INF] Executing OkObjectResult, writing value of type 'System.Collections.Generic.List`1[[Hsse.Data.Entities.MstPostCategories, Hsse.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-05-31 15:43:38.857 +05:30 [INF] Executed action Hsse.API.Controllers.v1.PostController.GetPostCategories (Hsse.API) in 132.4404ms
2025-05-31 15:43:38.870 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.PostController.GetPostCategories (Hsse.API)'
2025-05-31 15:43:38.879 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Post/GetPostCategories - 200 null application/json; charset=utf-8 171.4373ms
2025-05-31 15:43:43.850 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Post/GetPosts - null null
2025-05-31 15:43:43.867 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:43:43.873 +05:30 [INF] Route matched with {action = "GetPosts", controller = "Post"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetPosts() on controller Hsse.API.Controllers.v1.PostController (Hsse.API).
2025-05-31 15:43:44.112 +05:30 [INF] Executed DbCommand (43ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[PostID], [m].[ClosedBy], [m].[CreatedAt], [m].[DeletedBy], [m].[Description], [m].[FacilityID], [m].[IsDeleted], [m].[Location], [m].[PostType], [m].[RequiresFollowup], [m].[Status], [m].[TaggedCategoryId], [m].[Title], [m].[UpdatedAt], [m].[UserID]
FROM [MstPosts] AS [m]
WHERE [m].[IsDeleted] = CAST(0 AS bit)
2025-05-31 15:43:44.138 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'Hsse.Data.Helper.MasterDBContext'.
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlBuffer.ThrowIfNull()
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method62(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlBuffer.ThrowIfNull()
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method62(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
2025-05-31 15:43:44.149 +05:30 [INF] Executed action Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API) in 265.9871ms
2025-05-31 15:43:44.151 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:43:44.158 +05:30 [ERR] An unhandled exception has occurred while executing the request.
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlBuffer.ThrowIfNull()
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method62(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Hsse.Infrastructure.Repositories.PostRepository.GetPosts() in D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.Infrastructure\Repositories\PostRepository.cs:line 107
   at Hsse.Application.Services.PostService.GetPosts() in D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.Application\Services\PostService.cs:line 27
   at lambda_method58(Closure, Object, Object[])
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-05-31 15:43:44.206 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Post/GetPosts - 500 null text/plain; charset=utf-8 355.2732ms
2025-05-31 15:44:47.281 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-31 15:44:47.685 +05:30 [INF] Now listening on: https://localhost:7231
2025-05-31 15:44:47.689 +05:30 [INF] Now listening on: http://localhost:5133
2025-05-31 15:44:47.834 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-31 15:44:47.872 +05:30 [INF] Hosting environment: Development
2025-05-31 15:44:47.875 +05:30 [INF] Content root path: D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.API
2025-05-31 15:44:49.822 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-05-31 15:44:50.233 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 423.1799ms
2025-05-31 15:44:50.262 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-05-31 15:44:50.267 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-05-31 15:44:50.287 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 25.3773ms
2025-05-31 15:44:50.366 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 98.8868ms
2025-05-31 15:44:51.002 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-05-31 15:44:51.022 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 19.7823ms
2025-05-31 15:44:57.051 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/api/v1/Post/GetPosts - null null
2025-05-31 15:44:57.157 +05:30 [INF] Executing endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:44:57.180 +05:30 [INF] Route matched with {action = "GetPosts", controller = "Post"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult GetPosts() on controller Hsse.API.Controllers.v1.PostController (Hsse.API).
2025-05-31 15:44:59.204 +05:30 [INF] Executed DbCommand (124ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [m].[PostID], [m].[ClosedBy], [m].[CreatedAt], [m].[DeletedBy], [m].[Description], [m].[FacilityID], [m].[IsDeleted], [m].[Location], [m].[PostType], [m].[RequiresFollowup], [m].[Status], [m].[TaggedCategoryId], [m].[Title], [m].[UpdatedAt], [m].[UserID]
FROM [MstPosts] AS [m]
2025-05-31 15:44:59.248 +05:30 [ERR] An exception occurred while iterating over the results of a query for context type 'Hsse.Data.Helper.MasterDBContext'.
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlBuffer.ThrowIfNull()
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method37(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlBuffer.ThrowIfNull()
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method37(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
2025-05-31 15:44:59.277 +05:30 [INF] Executed action Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API) in 2088.0332ms
2025-05-31 15:44:59.281 +05:30 [INF] Executed endpoint 'Hsse.API.Controllers.v1.PostController.GetPosts (Hsse.API)'
2025-05-31 15:44:59.288 +05:30 [ERR] An unhandled exception has occurred while executing the request.
System.Data.SqlTypes.SqlNullValueException: Data is Null. This method or property cannot be called on Null values.
   at Microsoft.Data.SqlClient.SqlBuffer.ThrowIfNull()
   at Microsoft.Data.SqlClient.SqlBuffer.get_String()
   at Microsoft.Data.SqlClient.SqlDataReader.GetString(Int32 i)
   at lambda_method37(Closure, QueryContext, DbDataReader, ResultContext, SingleQueryResultCoordinator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Collections.Generic.List`1..ctor(IEnumerable`1 collection)
   at System.Linq.Enumerable.ToList[TSource](IEnumerable`1 source)
   at Hsse.Infrastructure.Repositories.PostRepository.GetPosts() in D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.Infrastructure\Repositories\PostRepository.cs:line 107
   at Hsse.Application.Services.PostService.GetPosts() in D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.Application\Services\PostService.cs:line 27
   at lambda_method2(Closure, Object, Object[])
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.SyncActionResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeActionMethodAsync()
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeNextActionFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-05-31 15:44:59.348 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/api/v1/Post/GetPosts - 500 null text/plain; charset=utf-8 2297.2364ms
2025-05-31 16:34:41.276 +05:30 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-05-31 16:34:41.613 +05:30 [INF] Now listening on: https://localhost:7231
2025-05-31 16:34:41.617 +05:30 [INF] Now listening on: http://localhost:5133
2025-05-31 16:34:41.824 +05:30 [INF] Application started. Press Ctrl+C to shut down.
2025-05-31 16:34:41.866 +05:30 [INF] Hosting environment: Development
2025-05-31 16:34:41.868 +05:30 [INF] Content root path: D:\UEMS\HSSE\hsse-mobile-api\hsse-mobile-api\HsseAPI\Hsse.API
2025-05-31 16:34:42.606 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/index.html - null null
2025-05-31 16:34:43.027 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/index.html - 200 null text/html;charset=utf-8 430.0009ms
2025-05-31 16:34:43.051 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - null null
2025-05-31 16:34:43.056 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/_vs/browserLink - null null
2025-05-31 16:34:43.074 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_framework/aspnetcore-browser-refresh.js - 200 13752 application/javascript; charset=utf-8 22.35ms
2025-05-31 16:34:43.149 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/_vs/browserLink - 200 null text/javascript; charset=UTF-8 93.4263ms
2025-05-31 16:34:43.698 +05:30 [INF] Request starting HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - null null
2025-05-31 16:34:43.729 +05:30 [INF] Request finished HTTP/2 GET https://localhost:7231/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 30.6315ms
