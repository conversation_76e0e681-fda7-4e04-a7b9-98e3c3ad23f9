﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Entities
{
    public class MstPostMedia
    {
        [Key]
        public int MediaID { get; set; }
        public int PostID { get; set; }
        public string MediaURL { get; set; }
        public string AfterMediaURL { get; set; }
        public DateTime? CreatedAt { get; set; }
    }
}
