﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Entities;
using Hsse.Data.Helper;
using Hsse.Infrastructure.IRepositories;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.Repositories
{
    public class PostRepository : IPostRepository
    {
        private readonly MasterDBContext _MasterDBContext;
        public PostRepository(MasterDBContext masterDBContext)
        {
            _MasterDBContext = masterDBContext;
        }

        public long CreatePost(CreatePostDto createPostDto)
        {
            if (createPostDto.PostID == 0)
            {
                // Create new post
                var newPost = new MstPosts
                {
                    UserID = createPostDto.UserID,
                    FacilityID = createPostDto.FacilityID,
                    Title = createPostDto.Title,
                    Description = createPostDto.Description,
                    PostType = createPostDto.PostType,
                    Location = createPostDto.Location,
                    TaggedCategoryId = createPostDto.TaggedCategoryId,
                    RequiresFollowup = createPostDto.RequiresFollowup,
                    Status = createPostDto.Status,
                    CreatedAt = DateTime.Now,
                    IsDeleted = false
                };

                _MasterDBContext.MstPosts.Add(newPost);
                _MasterDBContext.SaveChanges();

                if (!string.IsNullOrEmpty(createPostDto.MediaURL))
                {
                    var media = new MstPostMedia
                    {
                        PostID = newPost.PostID,
                        MediaURL = createPostDto.MediaURL,
                        CreatedAt = DateTime.Now
                    };

                    _MasterDBContext.MstPostMedia.Add(media);
                    _MasterDBContext.SaveChanges();
                }

                return newPost.PostID;
            }
            else
            {
                // Update existing post
                var existingPost = _MasterDBContext.MstPosts
                    .FirstOrDefault(p => p.PostID == createPostDto.PostID && !p.IsDeleted);

                if (existingPost == null)
                    return 0;

                existingPost.UserID = createPostDto.UserID;
                existingPost.FacilityID = createPostDto.FacilityID;
                existingPost.Title = createPostDto.Title;
                existingPost.Description = createPostDto.Description;
                existingPost.PostType = createPostDto.PostType;
                existingPost.Location = createPostDto.Location;
                existingPost.TaggedCategoryId = createPostDto.TaggedCategoryId;
                existingPost.RequiresFollowup = createPostDto.RequiresFollowup;
                existingPost.Status = createPostDto.Status;
                existingPost.UpdatedAt = DateTime.Now;

                var existingMedia = _MasterDBContext.MstPostMedia
                    .FirstOrDefault(m => m.PostID == createPostDto.PostID);

                if (!string.IsNullOrEmpty(createPostDto.MediaURL))
                {
                    if (existingMedia != null)
                    {
                        existingMedia.MediaURL = createPostDto.MediaURL;
                    }
                    else
                    {
                        var newMedia = new MstPostMedia
                        {
                            PostID = createPostDto.PostID,
                            MediaURL = createPostDto.MediaURL,
                            CreatedAt = DateTime.Now
                        };
                        _MasterDBContext.MstPostMedia.Add(newMedia);
                    }
                }

                _MasterDBContext.SaveChanges();
                return existingPost.PostID;
            }
        }
        public List<MstPosts> GetPosts()
        {
           return _MasterDBContext.MstPosts.ToList();
        }
        public List<MstPostCategories> GetPostCategories()
        {
            return _MasterDBContext.MstPostCategories.ToList();
        }
        public int CreateOrUpdateLikes(CreateLikeDto createLikeDto)
        {
            // Ensure exactly one of PostID or EventID is provided
            if ((createLikeDto.PostID == null && createLikeDto.EventID == null) ||
                (createLikeDto.PostID != null && createLikeDto.EventID != null))
            {
                throw new ArgumentException("Like must be for either a Post or an Event, not both.");
            }

            // Query only for one of the non-null keys
            var result = _MasterDBContext.MstLikesConfig.FirstOrDefault(x =>
                x.UserID == createLikeDto.UserID &&
                ((createLikeDto.PostID != null && x.PostID == createLikeDto.PostID) ||
                 (createLikeDto.EventID != null && x.EventID == createLikeDto.EventID)));

            if (result == null)
            {
                var newLike = new MstLikesConfig
                {
                    UserID = createLikeDto.UserID,
                    PostID = createLikeDto.PostID,
                    EventID = createLikeDto.EventID,
                    IsLiked = createLikeDto.IsLiked,
                    LikedAt = DateTime.Now
                };

                _MasterDBContext.MstLikesConfig.Add(newLike);
                _MasterDBContext.SaveChanges();
                return 1; // Created
            }

            // Update existing
            result.IsLiked = createLikeDto.IsLiked;
            result.LikedAt = DateTime.Now;

            _MasterDBContext.SaveChanges();
            return 2; // Updated
        }


    }
}
