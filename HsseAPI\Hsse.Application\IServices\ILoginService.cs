﻿using Hsse.Data.Dto.Response;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Application.IServices
{
    public interface ILoginService
    {
        public IEnumerable<EmployeeDetails> ValidateUser(string? employeeEmail, string? employeeId, string? fCMToken);
        public IEnumerable<EmployeeFacility> GetUserFacility(string? EmployeeEmail);
        Task<int> LogoutUser(int UserId);
    }
}
