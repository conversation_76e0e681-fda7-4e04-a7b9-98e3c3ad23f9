﻿using Hsse.Data.Dto.Response;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.IRepositories
{
    public interface ILoginRepository
    {
        IEnumerable<EmployeeDetails> ValidateUser(string? employeeEmail, string? employeeId, string? fCMToken);
        IEnumerable<EmployeeFacility> GetUserFacility(string? EmployeeEmail);
        Task<int> LogoutUser(int UserId);
    } 
}
