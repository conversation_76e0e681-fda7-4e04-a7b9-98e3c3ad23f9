﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Dto.Response
{
    public class EmployeeDetails
    {
        public int EmployeeId { get; set; }
        public string? EmployeeNameId { get; set; }
        public string? EmployeeName { get; set; }
        public string? Role { get; set; }
        public int FacilityId { get; set; }
        public string? FacilityName { get; set; }
        public string? FacilityCode { get; set; }
        public string? ImageURL { get; set; }
        public string? LastLogin { get; set; }
        public string? EmailID { get; set; }
        public string? Token { get; set; }


    }
    public class EmployeeDropDownDetails
    {
        public int EmployeeId { get; set; }
        public string? EmployeeNameId { get; set; }
        public string? EmployeeName { get; set; }

        public string? EmployeeEmail { get; set; }
    }

    public class EmployeeFacility
    {
        public int FacilityId { get; set; }
        public string? FacilityName { get; set; }
    }

    public class EmployeeDetailsList
    {
        public IList<EmployeeDetails> EmployeeDetails { get; set; }

        public IList<EmployeeFacility> EmployeeFacility { get; set; }

    }
}
