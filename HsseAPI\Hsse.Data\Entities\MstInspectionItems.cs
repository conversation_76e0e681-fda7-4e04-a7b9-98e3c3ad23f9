﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Data.Entities
{
    public class MstInspectionItems
    {
        [Key]
        public int ItemId { get; set; }
        public int InspectionId { get; set; }
        public string Description { get; set; }
        public string SpecificLocation { get; set; }
        public string Recommendation { get; set; }
        public int? ActionPartyName { get; set; }
        public int? Status { get; set; }
        public string Rectification { get; set; }
        public string AfterImagePath { get; set; }
        public DateTime? CompletionDateTime { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? ModifiedAt { get; set; }
        public string RecommendationMediaUrl { get; set; }
        public string Observation { get; set; }
        public string ObservationMediaUrl { get; set; }
        public int? Verification { get; set; }
        public int? ContactPersonId { get; set; }
        public int? TypeOfInspection { get; set; }
    }
}
