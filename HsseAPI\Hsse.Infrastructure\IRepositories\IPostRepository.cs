﻿using Hsse.Data.Dto.Request;
using Hsse.Data.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Hsse.Infrastructure.IRepositories
{
    public interface IPostRepository
    {
        long CreatePost(CreatePostDto createPostDto);
        List<MstPosts> GetPosts();
        List<MstPostCategories> GetPostCategories();
        int CreateOrUpdateLikes(CreateLikeDto createLikeDto);
    }
}
