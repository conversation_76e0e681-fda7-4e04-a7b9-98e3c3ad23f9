﻿using Dapper;
using Hsse.Data.Dto.Response;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Hsse.Infrastructure.IRepositories;
using Microsoft.Data.SqlClient;
using System.Reflection;
using Microsoft.Extensions.Logging;
using Serilog.Core;

namespace Hsse.Infrastructure.Repositories
{
    public class LoginRepository : ILoginRepository
    {
        private readonly IConfiguration _IConfiguration;
        private IDbConnection db;
        private readonly ILogger<LoginRepository> _logger;
        private readonly string _connectionString;

        public LoginRepository(IConfiguration IConfiguration, ILogger<LoginRepository> logger)
        {
            _IConfiguration = IConfiguration;
            _logger = logger;
            _connectionString = _IConfiguration.GetConnectionString("HsseDB");
        }

        public IEnumerable<EmployeeDetails> ValidateUser(string? employeeEmail, string? employeeId, string? fCMToken)
        {

            using (SqlConnection myCon = new SqlConnection(_connectionString))
                try
                {
                    var sql = "usp_get_employee_details";
                    using (myCon)
                    {
                        var imagePath = _IConfiguration?.GetSection("AzureConnectionConfig:ImagePath").Value + "Profile/";
                        myCon.Open();
                        var p = new DynamicParameters();
                        p.Add("@EmployeeEmailId", employeeEmail);
                        p.Add("@EmployeeNameId", employeeId);
                        p.Add("@FCMToken", fCMToken);
                        p.Add("@ImagePath", imagePath);
                        var result = myCon.Query<EmployeeDetails>(sql, p, commandType: CommandType.StoredProcedure);
                        return result;
                    }
                }
                catch (Exception ex)
                {
                    LogError(ex);
                    throw;
                }

        }

        public IEnumerable<EmployeeFacility> GetUserFacility(string? EmployeeEmail)
        {

            using (SqlConnection myCon = new SqlConnection(_connectionString))
                try
                {
                    var sql = "usp_get_employee_facilities";
                    using (myCon)
                    {
                        myCon.Open();
                        var p = new DynamicParameters();
                        p.Add("@EmployeeEmailId", EmployeeEmail);
                        var result = myCon.Query<EmployeeFacility>(sql, p, commandType: CommandType.StoredProcedure);
                        return result;
                    }
                }
                catch (Exception ex)
                {
                    LogError(ex);
                    throw;
                }

        }
        public async Task<int> LogoutUser(int UserId)
        {

            using (SqlConnection myCon = new SqlConnection(_connectionString))
                try
                {

                    using (myCon)
                    {
                        myCon.Open();
                        var p = new DynamicParameters();
                        p.Add("@UserId", UserId);
                        p.Add("@Result", dbType: DbType.Int32, direction: ParameterDirection.Output);
                        const string storedProcedure = "usp_user_logout";
                        await myCon.QueryAsync<int>(storedProcedure, p, commandType: CommandType.StoredProcedure);
                        var result = p.Get<int>("Result");
                        return result;
                    }
                }
                catch (Exception ex)
                {
                    return 0;
                }
        }


        private void LogError(Exception ex)
        {
            var methodName = MethodBase.GetCurrentMethod()?.Name;
            _logger.LogError(ex, "Method: {MethodName}, Error: {ErrorMessage}", methodName, ex.Message);
        }
    }
}
