﻿using Asp.Versioning;
using Hsse.Application.IServices;
using Hsse.Data.Dto.Request;
using Microsoft.AspNetCore.Mvc;

namespace Hsse.API.Controllers.v1
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class PostController : ControllerBase
    {
        private readonly IPostService _IPostService;
        private readonly ILogger<PostController> _logger;
        public PostController(IPostService postService, IConfiguration configuration, ILogger<PostController> logger)
        {
            _logger = logger;
            _IPostService = postService;
        }
        [HttpPost("CreatePost")]
        public IActionResult CreatePost([FromBody] CreatePostDto createPostDto) 
        {
            int result = (int)_IPostService.CreatePost(createPostDto);
            return Ok(result);
        }
        [HttpGet("GetPosts")]
        public IActionResult GetPosts()
        {
            var result = _IPostService.GetPosts();
            return Ok(result);
        }
        [HttpGet("GetPostCategories")]
        public IActionResult GetPostCategories()
        {
            var result = _IPostService.GetPostCategories();
            return Ok(result);
        }
        [HttpPost("CreateOrUpdateLikes")]
        public IActionResult CreateOrUpdateLikes([FromBody] CreateLikeDto createLikeDto)
        {
            int result = (int)_IPostService.CreateOrUpdateLikes(createLikeDto);
            return Ok(result);
        }
    }
}
